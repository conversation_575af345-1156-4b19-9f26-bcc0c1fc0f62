cmake_minimum_required(VERSION 3.10)
project(TaskSchedulerDemo)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置为Windows应用程序
if(WIN32)
    set(CMAKE_SYSTEM_VERSION 10.0)
endif()

# 添加可执行文件
add_executable(TaskSchedulerDemo task_scheduler.cpp)

# 链接Windows库
if(WIN32)
    target_link_libraries(TaskSchedulerDemo 
        taskschd
        comsupp
        ole32
        oleaut32
    )
endif()

# 设置编译选项
if(MSVC)
    target_compile_options(TaskSchedulerDemo PRIVATE /W4)
else()
    target_compile_options(TaskSchedulerDemo PRIVATE -Wall -Wextra)
endif()
