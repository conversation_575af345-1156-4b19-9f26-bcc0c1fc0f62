@echo off
echo Building Task Scheduler Demo...

REM Create build directory
if not exist build mkdir build
cd build

REM Use CMake to generate project files
cmake .. -G "Visual Studio 17 2022" -A x64

REM If CMake fails, try other generators
if %errorlevel% neq 0 (
    echo Trying with Visual Studio 16 2019...
    cmake .. -G "Visual Studio 16 2019" -A x64
)

if %errorlevel% neq 0 (
    echo Trying with Visual Studio 15 2017...
    cmake .. -G "Visual Studio 15 2017" -A x64
)

if %errorlevel% neq 0 (
    echo Trying with MinGW Makefiles...
    cmake .. -G "MinGW Makefiles"
)

REM Build project
if %errorlevel% equ 0 (
    echo Building project...
    cmake --build . --config Release

    if %errorlevel% equ 0 (
        echo Build successful!
        echo Running the program...
        echo.
        Release\TaskSchedulerDemo.exe
    ) else (
        echo Build failed!
    )
) else (
    echo CMake configuration failed!
)

cd ..
pause
