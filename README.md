# Windows Task Scheduler Demo

这个C++程序使用Windows Task Scheduler API创建一个计划任务，每1分钟运行一次notepad.exe。

## 功能特性

- 使用Windows Task Scheduler COM API
- 创建每分钟运行的计划任务
- 兼容Windows Vista及以上版本（包括Win7, Win8, Win10, Win11）
- 支持任务的创建和删除
- 错误处理和状态反馈

## 系统要求

- Windows Vista 或更高版本
- Visual Studio 2017 或更高版本（或MinGW）
- CMake 3.10 或更高版本

## 编译和运行

### 方法1：使用批处理文件（推荐）
```bash
build.bat
```

### 方法2：手动编译
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
Release\TaskSchedulerDemo.exe
```

### 方法3：直接使用Visual Studio编译器
```bash
cl /EHsc task_scheduler.cpp /link taskschd.lib comsupp.lib ole32.lib oleaut32.lib
```

## 使用说明

1. 以**管理员权限**运行程序（推荐）
2. 程序会创建名为"NotepadEveryMinute"的计划任务
3. 任务将每分钟启动一次notepad.exe
4. 可以选择删除任务或直接退出

## 验证任务

创建任务后，可以通过以下方式验证：

1. 打开任务计划程序：`Win + R` → `taskschd.msc`
2. 在任务计划程序库中查找"NotepadEveryMinute"任务
3. 观察notepad.exe是否每分钟启动一次

## 兼容性

该程序使用标准的Windows Task Scheduler API，兼容以下系统：

- Windows Vista
- Windows 7
- Windows 8/8.1
- Windows 10
- Windows 11
- Windows Server 2008及以上版本

## 注意事项

1. **权限要求**：创建计划任务通常需要管理员权限
2. **防火墙/杀毒软件**：某些安全软件可能会阻止程序创建计划任务
3. **任务持久性**：创建的任务会持久保存，直到手动删除
4. **资源使用**：每分钟启动notepad可能会消耗系统资源

## 故障排除

### 错误代码说明
- `0x80070005`：访问被拒绝，需要管理员权限
- `0x80041318`：任务已存在
- `0x80041309`：任务服务未运行

### 常见问题
1. **编译错误**：确保安装了Windows SDK
2. **运行时错误**：以管理员身份运行程序
3. **任务不执行**：检查任务计划程序服务是否运行

## 代码结构

- `TaskSchedulerManager`类：封装了Task Scheduler API的操作
- `Initialize()`：初始化COM和Task Scheduler服务
- `CreateNotepadTask()`：创建每分钟运行notepad的任务
- `DeleteTask()`：删除指定的任务
- `Cleanup()`：清理资源

## 自定义修改

要修改任务行为，可以调整以下参数：

1. **执行间隔**：修改`put_Interval(_bstr_t(L"PT1M"))`中的"PT1M"
   - PT30S = 30秒
   - PT5M = 5分钟
   - PT1H = 1小时

2. **执行程序**：修改`put_Path(_bstr_t(L"notepad.exe"))`

3. **任务名称**：修改`L"NotepadEveryMinute"`

## 许可证

此代码仅供学习和演示使用。
