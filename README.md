# Windows Task Scheduler Demo

这个C++程序使用Windows Task Scheduler API创建计划任务，支持自定义可执行文件、任务名称和执行间隔。

## 功能特性

- 使用Windows Task Scheduler COM API
- 支持命令行参数自定义任务配置
- 可指定任意可执行文件、任务名称和执行间隔
- 兼容Windows Vista及以上版本（包括Win7, Win8, Win10, Win11）
- 完全静态链接，无需额外DLL依赖
- 错误处理和状态反馈

## 系统要求

- Windows Vista 或更高版本
- Visual Studio 2017 或更高版本（或MinGW）
- CMake 3.10 或更高版本

## 编译和运行

### 方法1：使用批处理文件（推荐）
```bash
build.bat
```

### 方法2：手动编译
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
Release\TaskSchedulerDemo.exe
```

### 方法3：使用g++编译器（推荐，完全静态链接，解决所有依赖问题）
```bash
g++ -std=c++17 -static -o TaskSchedulerDemo.exe task_scheduler.cpp -ltaskschd -lole32 -loleaut32 -luuid
```

### 方法4：直接使用Visual Studio编译器
```bash
cl /EHsc task_scheduler.cpp /link taskschd.lib comsupp.lib ole32.lib oleaut32.lib
```

## 使用说明

### 命令行语法
```bash
TaskSchedulerDemo.exe [executable_path] [task_name] [interval_minutes]
```

### 参数说明
- `executable_path`: 要执行的程序的完整路径
- `task_name`: 计划任务的名称
- `interval_minutes`: 执行间隔（分钟）

### 使用示例

1. **自定义任务**（推荐用法）：
   ```bash
   TaskSchedulerDemo.exe c:\windows\system32\notepad.exe WindowsUpdates 120
   ```
   创建名为"WindowsUpdates"的任务，每120分钟运行notepad.exe

2. **默认任务**：
   ```bash
   TaskSchedulerDemo.exe
   ```
   创建默认任务"NotepadEveryMinute"，每分钟运行notepad.exe

### 注意事项
1. 以**管理员权限**运行程序（推荐）
2. 程序创建任务后自动退出
3. 可执行文件路径建议使用完整路径

## 验证任务

创建任务后，可以通过以下方式验证：

1. 打开任务计划程序：`Win + R` → `taskschd.msc`
2. 在任务计划程序库中查找创建的任务（如"WindowsUpdates"或"NotepadEveryMinute"）
3. 查看任务属性，确认执行程序和时间间隔设置正确
4. 观察程序是否按设定间隔启动

## 兼容性

该程序使用标准的Windows Task Scheduler API，兼容以下系统：

- Windows Vista
- Windows 7
- Windows 8/8.1
- Windows 10
- Windows 11
- Windows Server 2008及以上版本

## 注意事项

1. **权限要求**：创建计划任务通常需要管理员权限
2. **防火墙/杀毒软件**：某些安全软件可能会阻止程序创建计划任务
3. **任务持久性**：创建的任务会持久保存，直到手动删除
4. **资源使用**：每分钟启动notepad可能会消耗系统资源

## 故障排除

### 错误代码说明
- `0x80070005`：访问被拒绝，需要管理员权限
- `0x80041318`：任务已存在
- `0x80041309`：任务服务未运行

### 常见问题
1. **编译错误**：确保安装了Windows SDK
2. **运行时错误**：以管理员身份运行程序
3. **任务不执行**：检查任务计划程序服务是否运行
4. **运行时库缺失（libstdc++-6.dll, libwinpthread-1.dll等）**：使用完全静态链接编译选项 `-static`

## 代码结构

- `TaskSchedulerManager`类：封装了Task Scheduler API的操作
- `Initialize()`：初始化COM和Task Scheduler服务
- `CreateNotepadTask()`：创建每分钟运行notepad的任务
- `DeleteTask()`：删除指定的任务
- `Cleanup()`：清理资源

## 更多示例

```bash
# 每30分钟运行计算器
TaskSchedulerDemo.exe c:\windows\system32\calc.exe CalculatorTask 30

# 每2小时运行记事本
TaskSchedulerDemo.exe c:\windows\system32\notepad.exe NotepadTask 120

# 每10分钟运行自定义程序
TaskSchedulerDemo.exe "c:\program files\myapp\myapp.exe" MyAppTask 10
```

## 时间间隔说明

程序内部会将分钟数转换为ISO 8601持续时间格式：
- 1分钟 → PT1M
- 30分钟 → PT30M
- 120分钟 → PT120M

## 许可证

此代码仅供学习和演示使用。
