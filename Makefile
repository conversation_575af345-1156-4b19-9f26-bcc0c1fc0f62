# Makefile for Windows Task Scheduler Demo

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra
LIBS = -ltaskschd -lole32 -loleaut32 -luuid
TARGET = TaskSchedulerDemo.exe
SOURCE = task_scheduler.cpp

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)

# Clean build artifacts
clean:
	del /Q $(TARGET) 2>nul || true
	rmdir /S /Q build 2>nul || true

# Run the program
run: $(TARGET)
	./$(TARGET)

# Install (copy to system directory - requires admin)
install: $(TARGET)
	copy $(TARGET) C:\Windows\System32\

.PHONY: all clean run install
