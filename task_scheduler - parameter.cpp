#include <windows.h>
#include <comdef.h>
#include <taskschd.h>
#include <iostream>
#include <string>

#pragma comment(lib, "taskschd.lib")
#pragma comment(lib, "comsupp.lib")

class TaskSchedulerManager {
private:
    ITaskService* pService;
    ITaskFolder* pRootFolder;
    
public:
    TaskSchedulerManager() : pService(nullptr), pRootFolder(nullptr) {}
    
    ~TaskSchedulerManager() {
        Cleanup();
    }
    
    HRESULT Initialize() {
        HRESULT hr = CoInitializeEx(NULL, COINIT_MULTITHREADED);
        if (FAILED(hr)) {
            std::wcout << L"Failed to initialize COM library. Error: 0x" 
                      << std::hex << hr << std::endl;
            return hr;
        }
        
        hr = CoInitializeSecurity(
            NULL,
            -1,
            NULL,
            NULL,
            RPC_C_AUTHN_LEVEL_PKT_PRIVACY,
            RPC_C_IMP_LEVEL_IMPERSONATE,
            NULL,
            0,
            NULL);
        
        if (FAILED(hr)) {
            std::wcout << L"Failed to initialize security. Error: 0x" 
                      << std::hex << hr << std::endl;
            CoUninitialize();
            return hr;
        }
        
        hr = CoCreateInstance(CLSID_TaskScheduler,
                             NULL,
                             CLSCTX_INPROC_SERVER,
                             IID_ITaskService,
                             (void**)&pService);
        
        if (FAILED(hr)) {
            std::wcout << L"Failed to create TaskService instance. Error: 0x" 
                      << std::hex << hr << std::endl;
            CoUninitialize();
            return hr;
        }
        
        hr = pService->Connect(_variant_t(), _variant_t(), _variant_t(), _variant_t());
        if (FAILED(hr)) {
            std::wcout << L"Failed to connect to TaskService. Error: 0x" 
                      << std::hex << hr << std::endl;
            return hr;
        }
        
        hr = pService->GetFolder(_bstr_t(L"\\"), &pRootFolder);
        if (FAILED(hr)) {
            std::wcout << L"Failed to get root folder. Error: 0x" 
                      << std::hex << hr << std::endl;
            return hr;
        }
        
        return S_OK;
    }
    
    HRESULT CreateTask(const std::wstring& taskName, const std::wstring& executablePath, int intervalMinutes) {
        if (!pService || !pRootFolder) {
            std::wcout << L"TaskScheduler not initialized!" << std::endl;
            return E_FAIL;
        }

        // 删除已存在的任务（如果有）
        DeleteTask(taskName.c_str());
        
        ITaskDefinition* pTask = NULL;
        HRESULT hr = pService->NewTask(0, &pTask);
        if (FAILED(hr)) {
            std::wcout << L"Failed to create task definition. Error: 0x" 
                      << std::hex << hr << std::endl;
            return hr;
        }
        
        // 设置任务信息
        IRegistrationInfo* pRegInfo = NULL;
        hr = pTask->get_RegistrationInfo(&pRegInfo);
        if (SUCCEEDED(hr)) {
            pRegInfo->put_Author(_bstr_t(L"TaskScheduler Demo"));
            std::wstring description = L"Run " + executablePath + L" every " + std::to_wstring(intervalMinutes) + L" minutes";
            pRegInfo->put_Description(_bstr_t(description.c_str()));
            pRegInfo->Release();
        }
        
        // 设置主体信息
        IPrincipal* pPrincipal = NULL;
        hr = pTask->get_Principal(&pPrincipal);
        if (SUCCEEDED(hr)) {
            pPrincipal->put_LogonType(TASK_LOGON_INTERACTIVE_TOKEN);
            pPrincipal->put_RunLevel(TASK_RUNLEVEL_LUA);
            pPrincipal->Release();
        }
        
        // 设置任务设置
        ITaskSettings* pSettings = NULL;
        hr = pTask->get_Settings(&pSettings);
        if (SUCCEEDED(hr)) {
            pSettings->put_StartWhenAvailable(VARIANT_TRUE);
            pSettings->put_DisallowStartIfOnBatteries(VARIANT_FALSE);
            pSettings->put_StopIfGoingOnBatteries(VARIANT_FALSE);
            pSettings->put_AllowHardTerminate(VARIANT_TRUE);
            pSettings->put_MultipleInstances(TASK_INSTANCES_PARALLEL);
            pSettings->Release();
        }
        
        // 创建触发器集合
        ITriggerCollection* pTriggerCollection = NULL;
        hr = pTask->get_Triggers(&pTriggerCollection);
        if (FAILED(hr)) {
            std::wcout << L"Failed to get trigger collection. Error: 0x" 
                      << std::hex << hr << std::endl;
            pTask->Release();
            return hr;
        }
        
        // 创建时间触发器
        ITrigger* pTrigger = NULL;
        hr = pTriggerCollection->Create(TASK_TRIGGER_TIME, &pTrigger);
        if (FAILED(hr)) {
            std::wcout << L"Failed to create trigger. Error: 0x" 
                      << std::hex << hr << std::endl;
            pTriggerCollection->Release();
            pTask->Release();
            return hr;
        }
        
        ITimeTrigger* pTimeTrigger = NULL;
        hr = pTrigger->QueryInterface(IID_ITimeTrigger, (void**)&pTimeTrigger);
        if (SUCCEEDED(hr)) {
            pTimeTrigger->put_Id(_bstr_t(L"Trigger1"));
            
            // 设置开始时间为当前时间
            SYSTEMTIME st;
            GetLocalTime(&st);
            wchar_t startTime[50];
            swprintf_s(startTime, L"%04d-%02d-%02dT%02d:%02d:%02d",
                      st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
            
            pTimeTrigger->put_StartBoundary(_bstr_t(startTime));
            pTimeTrigger->put_Repetition(NULL);
            
            // 设置重复间隔
            IRepetitionPattern* pRepetition = NULL;
            hr = pTimeTrigger->get_Repetition(&pRepetition);
            if (SUCCEEDED(hr)) {
                std::wstring interval = L"PT" + std::to_wstring(intervalMinutes) + L"M";
                pRepetition->put_Interval(_bstr_t(interval.c_str()));
                pRepetition->put_Duration(_bstr_t(L"")); // 无限期重复
                pRepetition->Release();
            }
            
            pTimeTrigger->Release();
        }
        
        pTrigger->Release();
        pTriggerCollection->Release();
        
        // 创建动作集合
        IActionCollection* pActionCollection = NULL;
        hr = pTask->get_Actions(&pActionCollection);
        if (FAILED(hr)) {
            std::wcout << L"Failed to get action collection. Error: 0x" 
                      << std::hex << hr << std::endl;
            pTask->Release();
            return hr;
        }
        
        // 创建执行动作
        IAction* pAction = NULL;
        hr = pActionCollection->Create(TASK_ACTION_EXEC, &pAction);
        if (FAILED(hr)) {
            std::wcout << L"Failed to create action. Error: 0x" 
                      << std::hex << hr << std::endl;
            pActionCollection->Release();
            pTask->Release();
            return hr;
        }
        
        IExecAction* pExecAction = NULL;
        hr = pAction->QueryInterface(IID_IExecAction, (void**)&pExecAction);
        if (SUCCEEDED(hr)) {
            pExecAction->put_Path(_bstr_t(executablePath.c_str()));
            pExecAction->put_Arguments(_bstr_t(L""));
            pExecAction->Release();
        }
        
        pAction->Release();
        pActionCollection->Release();
        
        // 注册任务
        IRegisteredTask* pRegisteredTask = NULL;
        hr = pRootFolder->RegisterTaskDefinition(
            _bstr_t(taskName.c_str()),
            pTask,
            TASK_CREATE_OR_UPDATE,
            _variant_t(),
            _variant_t(),
            TASK_LOGON_INTERACTIVE_TOKEN,
            _variant_t(L""),
            &pRegisteredTask);

        if (SUCCEEDED(hr)) {
            std::wcout << L"Task '" << taskName << L"' created successfully!" << std::endl;
            std::wcout << L"The task will run " << executablePath << L" every " << intervalMinutes << L" minutes." << std::endl;
            std::wcout << L"You can view the task in Task Scheduler (taskschd.msc)" << std::endl;
            pRegisteredTask->Release();
        } else {
            std::wcout << L"Failed to register task. Error: 0x"
                      << std::hex << hr << std::endl;
        }
        
        pTask->Release();
        return hr;
    }

    // 保持向后兼容的方法
    HRESULT CreateNotepadTask() {
        return CreateTask(L"NotepadEveryMinute", L"notepad.exe", 1);
    }

    HRESULT DeleteTask(const wchar_t* taskName) {
        if (!pRootFolder) return E_FAIL;
        
        HRESULT hr = pRootFolder->DeleteTask(_bstr_t(taskName), 0);
        if (SUCCEEDED(hr)) {
            std::wcout << L"Task '" << taskName << L"' deleted successfully!" << std::endl;
        }
        // 忽略删除失败的错误（任务可能不存在）
        return S_OK;
    }
    
    void Cleanup() {
        if (pRootFolder) {
            pRootFolder->Release();
            pRootFolder = nullptr;
        }
        if (pService) {
            pService->Release();
            pService = nullptr;
        }
        CoUninitialize();
    }
};

void PrintUsage() {
    std::wcout << L"Usage: TaskSchedulerDemo.exe [executable_path] [task_name] [interval_minutes]" << std::endl;
    std::wcout << L"" << std::endl;
    std::wcout << L"Parameters:" << std::endl;
    std::wcout << L"  executable_path   - Full path to the executable to run" << std::endl;
    std::wcout << L"  task_name        - Name of the scheduled task" << std::endl;
    std::wcout << L"  interval_minutes - Interval in minutes between executions" << std::endl;
    std::wcout << L"" << std::endl;
    std::wcout << L"Example:" << std::endl;
    std::wcout << L"  TaskSchedulerDemo.exe c:\\windows\\system32\\notepad.exe WindowsUpdates 120" << std::endl;
    std::wcout << L"" << std::endl;
    std::wcout << L"If no parameters are provided, creates a default task 'NotepadEveryMinute'" << std::endl;
    std::wcout << L"that runs notepad.exe every minute." << std::endl;
}

int main(int argc, char* argv[]) {
    std::wcout << L"Windows Task Scheduler Demo" << std::endl;
    std::wcout << L"===========================" << std::endl;

    TaskSchedulerManager scheduler;

    HRESULT hr = scheduler.Initialize();
    if (FAILED(hr)) {
        std::wcout << L"Failed to initialize Task Scheduler!" << std::endl;
        return 1;
    }

    std::wcout << L"Task Scheduler initialized successfully." << std::endl;

    // 检查命令行参数
    if (argc == 1) {
        // 没有参数，使用默认设置
        std::wcout << L"No parameters provided, creating default task..." << std::endl;
        hr = scheduler.CreateNotepadTask();
    } else if (argc == 4) {
        // 有完整参数
        // 将char*转换为wstring
        std::string execPath(argv[1]);
        std::string taskName(argv[2]);
        int interval = std::atoi(argv[3]);

        if (interval <= 0) {
            std::wcout << L"Error: Interval must be a positive number!" << std::endl;
            PrintUsage();
            return 1;
        }

        std::wstring wExecPath(execPath.begin(), execPath.end());
        std::wstring wTaskName(taskName.begin(), taskName.end());

        std::wcout << L"Creating custom task..." << std::endl;
        std::wcout << L"Executable: " << wExecPath << std::endl;
        std::wcout << L"Task Name: " << wTaskName << std::endl;
        std::wcout << L"Interval: " << interval << L" minutes" << std::endl;

        hr = scheduler.CreateTask(wTaskName, wExecPath, interval);
    } else {
        // 参数数量不正确
        std::wcout << L"Error: Invalid number of parameters!" << std::endl;
        std::wcout << L"" << std::endl;
        PrintUsage();
        return 1;
    }

    if (FAILED(hr)) {
        std::wcout << L"Failed to create task!" << std::endl;
        return 1;
    }

    return 0;
}
