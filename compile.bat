@echo off
echo Compiling Task Scheduler Demo with static linking...
echo This will create a standalone executable that doesn't require any DLL dependencies.
echo.

g++ -std=c++17 -static -o TaskSchedulerDemo.exe task_scheduler.cpp -ltaskschd -lole32 -loleaut32 -luuid

if %errorlevel% equ 0 (
    echo.
    echo Compilation successful!
    echo Generated: TaskSchedulerDemo.exe
    echo File size: 
    dir TaskSchedulerDemo.exe | findstr TaskSchedulerDemo.exe
    echo.
    echo The executable is now ready to run on any Windows system without additional dependencies.
) else (
    echo.
    echo Compilation failed!
    echo Make sure you have g++ installed and Windows SDK available.
)

pause
